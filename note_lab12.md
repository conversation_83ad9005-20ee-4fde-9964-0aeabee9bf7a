# Lab12 - LTI系统响应仿真笔记

## 实验目标
对连续LTI系统进行数值仿真，分析系统对指数衰减输入的响应特性。

## 系统描述

### 微分方程
```
y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)
```

### 输入信号
```
f(t) = e^(-2t)u(t)
```
其中 u(t) 是单位阶跃函数。

## 理论分析

### 1. 系统特征方程
对应的齐次方程：`y''(t) + 2y'(t) + y(t) = 0`

特征方程：`s² + 2s + 1 = 0`

特征根：`s₁ = s₂ = -1` (重根)

齐次解：`yₕ(t) = (C₁ + C₂t)e^(-t)`

### 2. 输入信号分析
- `f(t) = e^(-2t)u(t)`
- `f'(t) = -2e^(-2t)u(t)` (对于 t > 0)
- 右端项：`f'(t) + 2f(t) = -2e^(-2t) + 2e^(-2t) = 0` (对于 t > 0)

**重要发现**：当 t > 0 时，右端项为 0，系统实际上是齐次的！

### 3. 数值求解方法
使用 MATLAB 的 `ode45` 求解器：
- 将二阶微分方程转换为一阶微分方程组
- 设置初始条件：y(0) = 0, y'(0) = 0
- 时间范围：0 到 5 秒

## 核心代码结构

### 主程序 (`lab12_LTI_system.m`)
1. **参数设置**：时间范围、初始条件
2. **数值求解**：使用 ode45 求解微分方程组
3. **结果处理**：插值获得绘图数据
4. **可视化**：绘制输入信号和系统响应
5. **数据输出**：显示关键时刻的数值

### 微分方程函数 (`system_ode.m`)
1. **状态变量定义**：y₁ = y(t), y₂ = y'(t)
2. **输入信号计算**：f(t) 和 f'(t)
3. **微分方程组**：
   - y₁' = y₂
   - y₂' = -2y₂ - y₁ + f'(t) + 2f(t)

## 预期结果

### 理论预测
由于 t > 0 时右端项为 0，且初始条件为零，系统响应应该恒为零：
```
y(t) = 0, 对于所有 t ≥ 0
```

### 数值验证
通过 MATLAB 仿真验证理论分析的正确性。

## 关键学习点

1. **微分方程数值解法**：ode45 求解器的使用
2. **状态空间表示**：二阶微分方程转一阶微分方程组
3. **LTI系统分析**：特征根、齐次解、特解的概念
4. **MATLAB编程**：函数文件、绘图、数据处理

## 扩展思考

1. 如果改变初始条件会如何？
2. 如果输入信号改为其他形式（如阶跃、冲激）会如何？
3. 系统参数变化对响应的影响？

## 文件说明

- `lab12_LTI_system.m`：主仿真程序
- `system_ode.m`：微分方程函数
- `note_lab12.md`：实验笔记（本文件）
- `lab12_LTI_response.png`：仿真结果图像（运行后生成）
