%% Lab12 - LTI系统响应仿真
% 微分方程: y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)
% 输入信号: f(t) = e^(-2t)u(t)
% 时间范围: 0-5秒

clear; clc; close all;

%% 参数设置
t_span = [0, 5];           % 时间范围
t_plot = linspace(0, 5, 1000); % 用于绘图的时间向量

%% 初始条件设置
% 假设系统初始处于静止状态
y0 = 0;      % y(0) = 0
dy0 = 0;     % y'(0) = 0
initial_conditions = [y0; dy0];

%% 求解微分方程
% 使用ode45求解器
[t_sol, y_sol] = ode45(@(t, y) system_ode(t, y), t_span, initial_conditions);

%% 计算输入信号 f(t) = e^(-2t)u(t)
f_input = exp(-2 * t_plot);  % u(t)在t>=0时为1，所以直接用exp(-2t)

%% 插值得到绘图用的解
y_response = interp1(t_sol, y_sol(:,1), t_plot, 'spline');

%% 绘制结果
figure('Position', [100, 100, 1200, 800]);

% 子图1: 输入信号
subplot(2, 1, 1);
plot(t_plot, f_input, 'b-', 'LineWidth', 2);
grid on;
xlabel('时间 t (秒)');
ylabel('输入信号 f(t)');
title('输入信号 f(t) = e^{-2t}u(t)');
xlim([0, 5]);
legend('f(t) = e^{-2t}u(t)', 'Location', 'northeast');

% 子图2: 系统响应
subplot(2, 1, 2);
plot(t_plot, y_response, 'r-', 'LineWidth', 2);
grid on;
xlabel('时间 t (秒)');
ylabel('系统响应 y(t)');
title('系统响应 y(t)');
xlim([0, 5]);
legend('y(t)', 'Location', 'northeast');

% 调整子图间距
sgtitle('LTI系统响应仿真 - Lab12', 'FontSize', 16, 'FontWeight', 'bold');

%% 显示数值结果
fprintf('=== Lab12 LTI系统响应仿真结果 ===\n');
fprintf('微分方程: y''''(t) + 2y''(t) + y(t) = f''(t) + 2f(t)\n');
fprintf('输入信号: f(t) = e^(-2t)u(t)\n');
fprintf('时间范围: 0 - 5 秒\n');
fprintf('初始条件: y(0) = %.2f, y''(0) = %.2f\n', y0, dy0);
fprintf('\n关键时刻的系统响应值:\n');

% 显示几个关键时刻的值
key_times = [0, 0.5, 1, 2, 3, 4, 5];
for i = 1:length(key_times)
    t_val = key_times(i);
    y_val = interp1(t_sol, y_sol(:,1), t_val, 'spline');
    f_val = exp(-2 * t_val);
    fprintf('t = %.1f秒: y(t) = %.4f, f(t) = %.4f\n', t_val, y_val, f_val);
end

%% 保存图像
saveas(gcf, 'lab12_LTI_response.png');
fprintf('\n图像已保存为: lab12_LTI_response.png\n');
